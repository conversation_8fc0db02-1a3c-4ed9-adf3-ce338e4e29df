<?php

declare(strict_types=1);

namespace App\Application\Admin\Form\Type;

use App\Domain\Cycling\Ranking\RankingType;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\Options;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class RankingTypeEntityType extends AbstractType
{
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver
            ->setRequired('tournament_id')
            ->setDefaults([
                'class' => RankingType::class,
                'choice_label' => 'name',
                'query_builder' => static function (Options $options) {
                    $tournamentId = $options['tournament_id'];

                    return static fn (EntityRepository $repo) => $repo->createQueryBuilder('rt')
                        ->join('rt.ranking', 'r')
                        ->where('r.tournament = :tournamentId')
                        ->setParameter('tournamentId', $tournamentId);
                },
            ]);
    }

    public function getParent(): string
    {
        return EntityType::class;
    }
}
