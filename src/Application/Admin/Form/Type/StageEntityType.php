<?php

declare(strict_types=1);

namespace App\Application\Admin\Form\Type;

use App\Domain\Cycling\Stage\Stage;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\Options;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class StageEntityType extends AbstractType
{
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver
            ->setRequired('tournament_id')
            ->setDefaults([
                'class' => Stage::class,
                'choice_label' => 'name',
                'query_builder' => static function (Options $options) {
                    $tournamentId = $options['tournament_id'];

                    return static fn (EntityRepository $repo) => $repo->createQueryBuilder('s')
                        ->where('s.tournament = :tournamentId')
                        ->setParameter('tournamentId', $tournamentId);
                },
            ]);
    }

    public function getParent(): string
    {
        return EntityType::class;
    }
}
