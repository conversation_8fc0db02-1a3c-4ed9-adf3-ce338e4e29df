<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Cyclist;

use App\Domain\Cycling\Team\Team;
use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_cyclist')]
class Cyclist
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    private string $firstName;

    #[ORM\Column(type: Types::STRING)]
    private string $lastName;

    #[ORM\Column(type: Types::STRING)]
    private string $country;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?DateTimeImmutable $inactiveAt;

    #[ORM\ManyToOne(targetEntity: Team::class, inversedBy: 'cyclists')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private Team $team;

    public function __construct(
        string $firstName,
        string $lastName,
        string $country,
        Team $team,
        bool $isActive,
    ) {
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->country = $country;
        $this->team = $team;
        $this->inactiveAt = $isActive ? null : new DateTimeImmutable();
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(
        string $firstName,
        string $lastName,
        string $country,
        Team $team,
        bool $isActive,
    ): void {
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->country = $country;
        $this->team = $team;
        $this->inactiveAt = $isActive ? null : new DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function getFullName(): string
    {
        return \sprintf('%s %s', $this->firstName, $this->lastName);
    }

    public function getSelect(): string
    {
        return \sprintf('%s %s (%s)', $this->firstName, $this->lastName, $this->team->getAbbreviation());
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getInactiveAt(): ?DateTimeImmutable
    {
        return $this->inactiveAt;
    }

    public function isActive(): bool
    {
        return null === $this->inactiveAt;
    }

    public function getTeam(): Team
    {
        return $this->team;
    }

    public function setTeam(Team $team): void
    {
        $this->team = $team;
    }

    public function __toString(): string
    {
        return $this->getFullName();
    }
}
