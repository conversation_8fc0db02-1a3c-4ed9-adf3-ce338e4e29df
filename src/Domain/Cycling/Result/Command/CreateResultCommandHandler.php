<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

use App\Domain\Cycling\Ranking\Resolver\RankingTypeResolver;
use App\Domain\Cycling\Result\Repository\ResultRepository;
use App\Domain\Cycling\Result\Result;
use App\Domain\Cycling\Stage\Resolver\StageResolver;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateResultCommandHandler
{
    public function __construct(
        private ResultRepository $resultRepository,
        private RankingTypeResolver $rankingTypeResolver,
        private TournamentResolver $tournamentResolver,
        private StageResolver $stageResolver,
    ) {
    }

    public function __invoke(CreateResultCommand $command): Result
    {
        $rankingType = $this->rankingTypeResolver->resolve($command->rankingTypeId);
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);
        $stage = $command->stageId ? $this->stageResolver->resolve($command->stageId) : null;

        $result = new Result(
            $tournament,
            $command->number,
            $rankingType,
            $stage,
        );

        $this->resultRepository->save($result);

        return $result;
    }
}
