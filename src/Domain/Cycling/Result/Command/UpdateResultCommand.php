<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

final readonly class UpdateResultCommand
{
    /**
     * @param array<int, array{id: ?int, position: int, cyclistId: int}> $cyclists
     */
    public function __construct(
        public int $id,
        public int $number,
        public int $rankingTypeId,
        public ?int $stageId = null,
        public array $cyclists = [],
    ) {
    }
}
