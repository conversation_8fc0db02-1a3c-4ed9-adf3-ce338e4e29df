<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

use App\Domain\Cycling\Ranking\Resolver\RankingTypeResolver;
use App\Domain\Cycling\Result\Repository\ResultRepository;
use App\Domain\Cycling\Result\Resolver\ResultResolver;
use App\Domain\Cycling\Result\Result;
use App\Domain\Cycling\Result\ResultCyclist;
use App\Domain\Cycling\Stage\Resolver\StageResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsMessageHandler]
final readonly class UpdateResultCommandHandler
{
    public function __construct(
        private ResultRepository $resultRepository,
        private ResultResolver $resultResolver,
        private RankingTypeResolver $rankingTypeResolver,
        private StageResolver $stageResolver,
        private MessageBusInterface $messageBus,
    ) {
    }

    public function __invoke(UpdateResultCommand $command): Result
    {
        $result = $this->resultResolver->resolve($command->id);
        $rankingType = $this->rankingTypeResolver->resolve($command->rankingTypeId);
        $stage = $command->stageId ? $this->stageResolver->resolve($command->stageId) : null;

        $result->update(
            $command->number,
            $rankingType,
            $stage,
        );

        $this->resultRepository->save($result);

        $this->manageCyclists($result, $command->cyclists);

        return $result;
    }

    /**
     * @param array<int, array{id: ?int, position: int, cyclistId: int}> $cyclistsData
     */
    private function manageCyclists(Result $result, array $cyclistsData): void
    {
        $currentCyclists = $result->getCyclists()->toArray();
        $currentCyclistIds = array_map(static fn (ResultCyclist $resultCyclist) => $resultCyclist->getId(), $currentCyclists);

        $updatedCyclistIds = [];

        foreach ($cyclistsData as $cyclistData) {
            if (isset($cyclistData['id']) && null !== $cyclistData['id']) {
                $updatedCyclistIds[] = $cyclistData['id'];
                $this->messageBus->dispatch(new UpdateResultCyclistCommand(
                    $cyclistData['id'],
                    $cyclistData['position'],
                    $cyclistData['cyclistId'],
                ));
            } else {
                $this->messageBus->dispatch(new CreateResultCyclistCommand(
                    $cyclistData['position'],
                    $cyclistData['cyclistId'],
                    $result->getId(),
                ));
            }
        }

        $cyclistsToDelete = array_diff($currentCyclistIds, $updatedCyclistIds);
        foreach ($cyclistsToDelete as $cyclistId) {
            $this->messageBus->dispatch(new DeleteResultCyclistCommand($cyclistId));
        }
    }
}
