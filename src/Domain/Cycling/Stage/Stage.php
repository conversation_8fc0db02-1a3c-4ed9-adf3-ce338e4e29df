<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Stage;

use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_stage')]
class Stage
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(type: Types::INTEGER)]
    private int $number;

    #[ORM\Column(type: Types::STRING, name: '`from`')]
    private string $from;

    #[ORM\Column(type: Types::STRING, name: '`to`')]
    private string $to;

    #[ORM\Column(type: Types::INTEGER)]
    private int $distance;

    #[ORM\Column(type: Types::DATE_IMMUTABLE)]
    private DateTimeImmutable $date;

    #[ORM\ManyToOne(targetEntity: Tournament::class, inversedBy: 'stages')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private Tournament $tournament;

    /**
     * @var Collection<int, \App\Domain\Cycling\Result\Result>
     */
    #[ORM\OneToMany(targetEntity: \App\Domain\Cycling\Result\Result::class, mappedBy: 'stage')]
    private Collection $results;

    public function __construct(
        int $number,
        string $from,
        string $to,
        int $distance,
        DateTimeImmutable $date,
        Tournament $tournament,
    ) {
        $this->number = $number;
        $this->from = $from;
        $this->to = $to;
        $this->distance = $distance;
        $this->date = $date;
        $this->tournament = $tournament;
        $this->results = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(
        int $number,
        string $from,
        string $to,
        int $distance,
        DateTimeImmutable $date,
        Tournament $tournament,
    ): void {
        $this->number = $number;
        $this->from = $from;
        $this->to = $to;
        $this->distance = $distance;
        $this->date = $date;
        $this->tournament = $tournament;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNumber(): int
    {
        return $this->number;
    }

    public function getName(): string
    {
        return \sprintf('%s - %s', $this->from, $this->to);
    }

    public function getFrom(): string
    {
        return $this->from;
    }

    public function getTo(): string
    {
        return $this->to;
    }

    public function getDistance(): int
    {
        return $this->distance;
    }

    public function getDate(): DateTimeImmutable
    {
        return $this->date;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getTournament(): ?Tournament
    {
        return $this->tournament;
    }

    public function setTournament(Tournament $tournament): void
    {
        $this->tournament = $tournament;
    }

    /**
     * @return Collection<int, \App\Domain\Cycling\Result\Result>
     */
    public function getResults(): Collection
    {
        return $this->results;
    }

    public function __toString(): string
    {
        return $this->getName();
    }
}
